import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import os
import sys
from pathlib import Path

# 确保正确的工作目录
current_dir = Path(__file__).parent
os.chdir(current_dir)
sys.path.insert(0, str(current_dir))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

# 导入模块
try:
    from langchain_community.callbacks.manager import get_openai_callback
    from src.graph.workflow import create_workflow
    from src.config.model_config import model_manager
except ImportError as e:
    st.error(f"模块导入失败: {e}")
    st.stop()

st.set_page_config(
    page_title="📊 智能数据分析助手",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        text-align: center;
        padding: 2rem 0;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    .upload-section {
        background: #f8f9fa;
        padding: 2rem;
        border-radius: 10px;
        margin: 1rem 0;
    }
    .analysis-section {
        background: #ffffff;
        padding: 2rem;
        border-radius: 10px;
        border: 1px solid #e9ecef;
        margin: 1rem 0;
    }
    .status-success {
        background: #d4edda;
        color: #155724;
        padding: 1rem;
        border-radius: 5px;
        border-left: 4px solid #28a745;
    }
    .status-error {
        background: #f8d7da;
        color: #721c24;
        padding: 1rem;
        border-radius: 5px;
        border-left: 4px solid #dc3545;
    }
</style>
""", unsafe_allow_html=True)

# 页面标题
st.markdown('<div class="main-header"><h1>📊 智能数据分析助手</h1><p>上传数据文件，用自然语言提问，获得专业的AI分析结果</p></div>', unsafe_allow_html=True)

@st.cache_resource
def get_model_manager():
    """获取模型管理器实例"""
    return model_manager

def check_model_status():
    """检查并配置AI模型"""
    # 强制重新加载环境变量
    load_dotenv(override=True)
    
    # 调试信息
    with st.expander("🔍 调试信息", expanded=False):
        st.write("环境变量检查:")
        deepseek_key = os.getenv('DEEPSEEK_API_KEY')
        st.write(f"- DEEPSEEK_API_KEY: {'✅ 已设置' if deepseek_key else '❌ 未设置'}")
        if deepseek_key:
            st.write(f"- 密钥前缀: {deepseek_key[:15]}...")
        
        st.write("工作目录信息:")
        st.write(f"- 当前目录: {os.getcwd()}")
        st.write(f"- .env文件存在: {os.path.exists('.env')}")
        
        # 重新初始化模型管理器
        try:
            mm = get_model_manager()
            st.write("模型管理器状态:")
            st.write(f"- 配置数量: {len(mm.configs)}")
            for key, config in mm.configs.items():
                st.write(f"- {key}: {config.name} ({'✅ 有密钥' if config.api_key else '❌ 无密钥'})")
        except Exception as e:
            st.error(f"模型管理器错误: {e}")
    
    try:
        mm = get_model_manager()
        available_models = mm.get_available_models()
        
        if not available_models:
            st.markdown('<div class="status-error">', unsafe_allow_html=True)
            st.error("❌ 未检测到可用的AI模型配置")
            st.markdown("""
            **请配置AI模型：**
            1. 检查项目根目录的 `.env` 文件
            2. 确保包含以下配置：
            ```
            DEEPSEEK_API_KEY=sk-your-deepseek-api-key
            ```
            3. 重启应用程序
            
            **获取API密钥：**
            - 访问 [DeepSeek官网](https://platform.deepseek.com/) 注册账号
            - 在API管理页面创建新的API密钥
            """)
            st.markdown('</div>', unsafe_allow_html=True)
            st.stop()
        
        # 自动选择第一个可用模型
        model_key = list(available_models.keys())[0]
        model_name = available_models[model_key]
        
        try:
            llm = mm.set_current_model(model_key)
            st.markdown('<div class="status-success">', unsafe_allow_html=True)
            st.success(f"✅ AI模型已就绪: **{model_name}**")
            st.markdown('</div>', unsafe_allow_html=True)
            return llm
        except Exception as e:
            st.markdown('<div class="status-error">', unsafe_allow_html=True)
            st.error(f"❌ 模型配置失败: {str(e)}")
            st.markdown('</div>', unsafe_allow_html=True)
            st.stop()
            
    except Exception as e:
        st.error(f"❌ 系统错误: {str(e)}")
        import traceback
        st.code(traceback.format_exc())
        st.stop()

# 检查并配置模型
llm = check_model_status()

# 功能介绍标签页
tab1, tab2 = st.tabs(["🎯 功能概览", "📖 使用说明"])

with tab1:
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        ### 📈 数据分析
        - 自动数据探索
        - 统计分析
        - 趋势识别
        - 异常检测
        """)
    
    with col2:
        st.markdown("""
        ### 🎨 智能可视化
        - 自动图表生成
        - 交互式图表
        - 多维度展示
        - 专业美观
        """)
    
    with col3:
        st.markdown("""
        ### 💡 智能洞察
        - 自然语言查询
        - 商业洞察
        - 实用建议
        - 专业报告
        """)

with tab2:
    st.markdown("""
    ### 📋 使用步骤
    
    1. **📁 上传数据文件**
       - 支持CSV和Excel格式
       - 文件大小限制200MB
       - 确保数据格式正确
    
    2. **👀 预览数据**
       - 检查数据是否正确加载
       - 查看数据结构和类型
       - 确认分析范围
    
    3. **💬 自然语言提问**
       - 用中文描述您的分析需求
       - 例如："分析销售趋势"、"找出最佳产品"
       - 支持复杂的分析请求
    
    4. **📊 获取分析结果**
       - AI自动生成分析计划
       - 执行数据处理和可视化
       - 提供专业的分析总结
    
    ### 💡 提问示例
    - "分析各地区的销售表现"
    - "找出影响销量的关键因素"
    - "预测下个月的销售趋势"
    - "比较不同产品的盈利能力"
    """)

# 文件上传区域
st.markdown('<div class="upload-section">', unsafe_allow_html=True)
st.markdown("### 📁 上传数据文件")

uploaded_file = st.file_uploader(
    "选择CSV或Excel文件",
    type=["csv", "xlsx", "xls"],
    help="支持CSV和Excel格式，最大200MB"
)
st.markdown('</div>', unsafe_allow_html=True)

if uploaded_file is not None:
    try:
        # 文件大小检查
        file_size = uploaded_file.size
        if file_size > 200 * 1024 * 1024:
            st.error("❌ 文件大小超过200MB限制")
            st.stop()
        
        # 读取文件
        with st.spinner("📖 正在读取文件..."):
            if uploaded_file.type == "text/csv" or uploaded_file.name.endswith('.csv'):
                # 尝试不同的编码方式
                try:
                    df = pd.read_csv(uploaded_file, encoding='utf-8')
                except UnicodeDecodeError:
                    try:
                        uploaded_file.seek(0)  # 重置文件指针
                        df = pd.read_csv(uploaded_file, encoding='gbk')
                    except UnicodeDecodeError:
                        uploaded_file.seek(0)  # 重置文件指针
                        df = pd.read_csv(uploaded_file, encoding='latin-1')
            else:
                df = pd.read_excel(uploaded_file)
        
        if df.empty:
            st.error("❌ 文件中没有数据")
            st.stop()
        
        # 创建工作流
        workflow = create_workflow(df)
        
        # 数据预览
        st.markdown('<div class="analysis-section">', unsafe_allow_html=True)
        st.markdown("### 📊 数据预览")
        
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("数据行数", f"{len(df):,}")
        with col2:
            st.metric("数据列数", len(df.columns))
        with col3:
            st.metric("文件大小", f"{file_size/1024/1024:.1f} MB")
        with col4:
            st.metric("数据类型", f"{df.dtypes.nunique()} 种")
        
        # 显示数据表格
        st.dataframe(
            df.head(100),  # 只显示前100行
            use_container_width=True,
            height=300
        )
        
        if len(df) > 100:
            st.info(f"💡 为了性能考虑，只显示前100行数据。实际数据包含 {len(df):,} 行。")
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # 分析查询区域
        st.markdown('<div class="analysis-section">', unsafe_allow_html=True)
        st.markdown("### 🔍 智能数据分析")
        
        question = st.text_area(
            "请用自然语言描述您的分析需求：",
            placeholder="例如：分析各地区的销售表现，找出表现最好的地区和产品组合",
            height=100,
            help="支持中文查询，可以提出复杂的分析需求"
        )
        
        col1, col2 = st.columns([3, 1])
        with col2:
            analyze_button = st.button("🚀 开始分析", use_container_width=True, type="primary")
        
        if analyze_button:
            if not question.strip():
                st.warning("⚠️ 请先输入您的分析需求")
                st.stop()
            
            try:
                with st.status("🤖 AI分析进行中...", expanded=True) as status:
                    st.write("🔄 正在理解您的需求...")
                    st.write("📋 正在制定分析计划...")
                    st.write("💻 正在生成分析代码...")
                    st.write("📊 正在执行数据分析...")
                    
                    with get_openai_callback() as cb:
                        state = workflow.invoke({
                            "user_query": question,
                            "error": None,
                            "iterations": 0
                        })
                    
                    status.update(label="✅ 分析完成!", state="complete")
                
                # 显示分析结果
                st.markdown("### 📋 分析计划")
                with st.expander("查看详细计划", expanded=False):
                    st.code(state.get("task_plan", "无可用计划"), language='text')
                
                st.markdown("### 💻 生成的代码")
                with st.expander("查看生成的代码", expanded=False):
                    st.code(state.get("code", "无可用代码"), language='python')
                
                # 分析结果
                if state.get("output"):
                    st.markdown("### 📈 分析结果")
                    
                    for key, value in state["output"].items():
                        if isinstance(value, pd.DataFrame):
                            st.markdown(f"#### 📊 {key}")
                            st.dataframe(value, use_container_width=True)
                        elif isinstance(value, go.Figure):
                            st.markdown(f"#### 📈 {key}")
                            st.plotly_chart(value, use_container_width=True)
                        elif isinstance(value, pd.Series):
                            st.markdown(f"#### 📊 {key}")
                            st.dataframe(pd.DataFrame(value), use_container_width=True)
                        else:
                            st.info(f"**{key}**: {value}")
                
                # 分析总结
                if state.get("answer"):
                    st.markdown("### 📌 分析总结")
                    st.success(state["answer"])
                
                # 使用统计
                col1, col2 = st.columns(2)
                with col1:
                    st.caption(f"🔢 使用Token数: {cb.total_tokens:,}")
                with col2:
                    st.caption(f"💰 分析成本: ${cb.total_cost:.4f}")
                
            except Exception as e:
                st.error(f"❌ 分析过程中出现错误: {str(e)}")
                st.info("💡 请尝试重新表述您的问题，或检查数据格式是否正确。")
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    except Exception as e:
        st.error(f"❌ 文件处理错误: {str(e)}")
        st.info("💡 请确保文件格式正确，数据完整。")

else:
    # 未上传文件时的提示
    st.markdown("""
    <div style="text-align: center; padding: 3rem; background: #f8f9fa; border-radius: 10px; margin: 2rem 0;">
        <h3>👆 请上传数据文件开始分析</h3>
        <p>支持CSV和Excel格式，最大200MB</p>
        <p>上传后即可开始智能数据分析之旅！</p>
    </div>
    """, unsafe_allow_html=True)

# 页面底部信息
st.markdown("---")
st.markdown("""
<div style="text-align: center; color: #6c757d; padding: 1rem;">
    <p>📊 智能数据分析助手 | 基于AI的专业数据分析工具</p>
    <p>💡 提示：支持中文查询，可处理复杂的数据分析需求</p>
</div>
""", unsafe_allow_html=True)
