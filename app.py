import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import os
import sys
from pathlib import Path

# 确保正确的工作目录
current_dir = Path(__file__).parent
os.chdir(current_dir)
sys.path.insert(0, str(current_dir))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

# 导入模块
try:
    from langchain_community.callbacks.manager import get_openai_callback
    from src.graph.workflow import create_workflow
    from src.config.model_config import model_manager
except ImportError as e:
    st.error(f"模块导入失败: {e}")
    st.stop()

st.set_page_config(
    page_title="📊 智能数据分析助手",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# 简洁的CSS样式
st.markdown("""
<style>
    .main-header {
        text-align: center;
        padding: 1.5rem 0;
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 10px;
        margin-bottom: 1.5rem;
    }
    .section {
        background: #ffffff;
        padding: 1.5rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

# 页面标题
st.markdown('<div class="main-header"><h1>📊 智能数据分析助手</h1><p>上传数据文件，用自然语言提问，获得专业的AI分析结果</p></div>', unsafe_allow_html=True)

@st.cache_resource
def get_model_manager():
    """获取模型管理器实例"""
    return model_manager

def check_model_status():
    """检查并配置AI模型"""
    try:
        mm = get_model_manager()
        available_models = mm.get_available_models()

        if not available_models:
            st.error("❌ AI模型未配置，请检查.env文件中的API密钥")
            st.stop()

        # 自动选择第一个可用模型
        model_key = list(available_models.keys())[0]
        model_name = available_models[model_key]

        llm = mm.set_current_model(model_key)
        st.success(f"✅ AI模型已就绪: **{model_name}**")
        return llm

    except Exception as e:
        st.error(f"❌ 模型配置失败: {str(e)}")
        st.stop()
        st.code(traceback.format_exc())
        st.stop()

# 检查并配置模型
llm = check_model_status()

# 文件上传区域
st.markdown('<div class="section">', unsafe_allow_html=True)
st.markdown("### 📁 上传数据文件")

uploaded_file = st.file_uploader(
    "选择CSV或Excel文件",
    type=["csv", "xlsx", "xls"],
    help="支持CSV和Excel格式，最大200MB"
)
st.markdown('</div>', unsafe_allow_html=True)

if uploaded_file is not None:
    try:
        # 文件大小检查
        file_size = uploaded_file.size
        if file_size > 200 * 1024 * 1024:
            st.error("❌ 文件大小超过200MB限制")
            st.stop()
        
        # 读取文件
        with st.spinner("📖 正在读取文件..."):
            if uploaded_file.type == "text/csv" or uploaded_file.name.endswith('.csv'):
                # 尝试不同的编码方式
                try:
                    df = pd.read_csv(uploaded_file, encoding='utf-8')
                except UnicodeDecodeError:
                    try:
                        uploaded_file.seek(0)  # 重置文件指针
                        df = pd.read_csv(uploaded_file, encoding='gbk')
                    except UnicodeDecodeError:
                        uploaded_file.seek(0)  # 重置文件指针
                        df = pd.read_csv(uploaded_file, encoding='latin-1')
            else:
                df = pd.read_excel(uploaded_file)
        
        if df.empty:
            st.error("❌ 文件中没有数据")
            st.stop()
        
        # 创建工作流
        workflow = create_workflow(df)
        
        # 数据预览
        st.markdown('<div class="section">', unsafe_allow_html=True)
        st.markdown("### 📊 数据预览")

        col1, col2 = st.columns(2)
        with col1:
            st.metric("数据行数", f"{len(df):,}")
        with col2:
            st.metric("数据列数", len(df.columns))

        # 显示数据表格
        try:
            st.dataframe(
                df.head(50),  # 显示前50行
                use_container_width=True,
                height=300
            )
        except Exception as e:
            if "pyarrow" in str(e).lower() or "arrow" in str(e).lower():
                st.warning("⚠️ 数据兼容性问题，正在自动修复...")
                from src.graph.workflow import clean_dataframe_for_arrow
                cleaned_df = clean_dataframe_for_arrow(df.head(50), "预览数据")
                st.dataframe(cleaned_df, use_container_width=True, height=300)
            else:
                st.error(f"显示数据时出错: {e}")

        st.markdown('</div>', unsafe_allow_html=True)
        
        # 智能数据分析
        st.markdown('<div class="section">', unsafe_allow_html=True)
        st.markdown("### 🔍 智能数据分析")

        question = st.text_area(
            "请用自然语言描述您的分析需求：",
            placeholder="例如：分析各地区的销售表现，找出表现最好的地区和产品组合",
            height=80
        )

        analyze_button = st.button("🚀 开始分析", type="primary")
        
        if analyze_button:
            if not question.strip():
                st.warning("⚠️ 请先输入您的分析需求")
                st.stop()
            
            try:
                with st.status("🤖 AI分析进行中...", expanded=False) as status:
                    with get_openai_callback() as cb:
                        state = workflow.invoke({
                            "user_query": question,
                            "error": None,
                            "iterations": 0
                        })
                    status.update(label="✅ 分析完成!", state="complete")
                
                # 分析结果
                if state.get("output"):
                    st.markdown("### 📈 分析结果")

                    for key, value in state["output"].items():
                        if isinstance(value, pd.DataFrame):
                            st.markdown(f"#### 📊 {key}")
                            try:
                                st.dataframe(value, use_container_width=True)
                            except Exception as e:
                                if "pyarrow" in str(e).lower() or "arrow" in str(e).lower():
                                    from src.graph.workflow import clean_dataframe_for_arrow
                                    cleaned_value = clean_dataframe_for_arrow(value, key)
                                    st.dataframe(cleaned_value, use_container_width=True)
                                else:
                                    st.error(f"显示 {key} 时出错: {e}")
                        elif isinstance(value, go.Figure):
                            st.markdown(f"#### 📈 {key}")
                            st.plotly_chart(value, use_container_width=True)
                        elif isinstance(value, pd.Series):
                            st.markdown(f"#### 📊 {key}")
                            try:
                                st.dataframe(pd.DataFrame(value), use_container_width=True)
                            except Exception as e:
                                if "pyarrow" in str(e).lower() or "arrow" in str(e).lower():
                                    from src.graph.workflow import clean_dataframe_for_arrow
                                    cleaned_series = clean_dataframe_for_arrow(pd.DataFrame(value), key)
                                    st.dataframe(cleaned_series, use_container_width=True)
                                else:
                                    st.error(f"显示 {key} 时出错: {e}")

                # 分析总结
                if state.get("answer"):
                    st.markdown("### 📌 分析总结")
                    st.success(state["answer"])
                
            except Exception as e:
                st.error(f"❌ 分析过程中出现错误: {str(e)}")
                st.info("💡 请尝试重新表述您的问题，或检查数据格式是否正确。")
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    except Exception as e:
        st.error(f"❌ 文件处理错误: {str(e)}")
        st.info("💡 请确保文件格式正确，数据完整。")

else:
    # 未上传文件时的提示
    st.info("👆 请上传数据文件开始分析")
