#!/usr/bin/env python3
"""
调试模型配置问题
"""

import os
from dotenv import load_dotenv

print("🔍 调试模型配置问题...")
print("=" * 50)

# 1. 检查环境变量加载
print("1. 检查环境变量:")
load_dotenv()
deepseek_key = os.getenv('DEEPSEEK_API_KEY')
print(f"   DEEPSEEK_API_KEY: {'✅ 已设置' if deepseek_key else '❌ 未设置'}")
if deepseek_key:
    print(f"   密钥前缀: {deepseek_key[:10]}...")

# 2. 检查模型管理器
print("\n2. 检查模型管理器:")
try:
    from src.config.model_config import model_manager
    print("   ✅ 模型管理器导入成功")
    
    # 检查配置
    configs = model_manager.configs
    print(f"   配置数量: {len(configs)}")
    
    for key, config in configs.items():
        print(f"   - {key}: {config.name}")
        print(f"     类型: {config.model_type}")
        print(f"     API密钥: {'✅ 已配置' if config.api_key else '❌ 未配置'}")
    
    # 检查可用模型
    available = model_manager.get_available_models()
    print(f"\n   可用模型数量: {len(available)}")
    for key, name in available.items():
        print(f"   - {key}: {name}")
    
    # 尝试创建模型实例
    if available:
        model_key = list(available.keys())[0]
        print(f"\n3. 测试模型实例创建:")
        print(f"   尝试创建: {model_key}")
        
        try:
            llm = model_manager.set_current_model(model_key)
            print("   ✅ 模型实例创建成功")
            print(f"   模型类型: {type(llm)}")
        except Exception as e:
            print(f"   ❌ 模型实例创建失败: {e}")
    else:
        print("\n3. ❌ 没有可用模型，无法测试实例创建")

except Exception as e:
    print(f"   ❌ 模型管理器导入失败: {e}")
    import traceback
    traceback.print_exc()

# 3. 检查依赖包
print("\n4. 检查关键依赖:")
required_packages = [
    'langchain_deepseek',
    'dotenv',
    'streamlit',
    'pandas'
]

for package in required_packages:
    try:
        __import__(package)
        print(f"   ✅ {package}")
    except ImportError as e:
        print(f"   ❌ {package}: {e}")

print("\n" + "=" * 50)
print("调试完成！")
