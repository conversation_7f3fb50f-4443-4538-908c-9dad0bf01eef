"""
Model configuration for the data analysis assistant.
Supports DeepSeek API and VLLM deployed local models.
"""

import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from langchain_deepseek import ChatDeepSeek


@dataclass
class ModelConfig:
    """Configuration for a model"""
    name: str
    model_type: str  # "deepseek" or "vllm"
    model_id: str
    base_url: Optional[str] = None
    api_key: Optional[str] = None
    temperature: float = 0.0
    top_p: float = 0.2
    max_tokens: Optional[int] = None


class ModelManager:
    """Manages model configurations and instances"""
    
    def __init__(self):
        self.configs = self._load_model_configs()
        self.current_model = None
    
    def _load_model_configs(self) -> Dict[str, ModelConfig]:
        """Load model configurations from environment or defaults"""
        configs = {}

        # DeepSeek API configuration
        deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')
        if deepseek_api_key:
            configs['deepseek-chat'] = ModelConfig(
                name="DeepSeek Chat",
                model_type="deepseek",
                model_id="deepseek-chat",
                api_key=deepseek_api_key,
                temperature=0.0,
                top_p=0.2
            )

        return configs

    def reload_configs(self):
        """Reload model configurations"""
        self.configs = self._load_model_configs()
        self.current_model = None
    
    def get_available_models(self) -> Dict[str, str]:
        """Get available models as {key: display_name}"""
        available = {}
        for key, config in self.configs.items():
            if self.is_model_available(key):
                available[key] = config.name
        return available
    
    def create_model_instance(self, model_key: str):
        """Create a model instance based on the configuration"""
        if model_key not in self.configs:
            raise ValueError(f"Model {model_key} not found in configurations")
        
        config = self.configs[model_key]
        
        if config.model_type == "deepseek":
            if not config.api_key:
                raise ValueError("DeepSeek API key is required but not configured")
            
            return ChatDeepSeek(
                model=config.model_id,
                temperature=config.temperature,
                top_p=config.top_p,
                api_key=config.api_key
            )
        

        
        else:
            raise ValueError(f"Unsupported model type: {config.model_type}")
    
    def set_current_model(self, model_key: str):
        """Set the current active model"""
        if model_key not in self.configs:
            raise ValueError(f"Model {model_key} not found in configurations")
        
        self.current_model = self.create_model_instance(model_key)
        return self.current_model
    
    def get_current_model(self):
        """Get the current active model instance"""
        return self.current_model
    
    def get_model_info(self, model_key: str) -> Dict[str, Any]:
        """Get information about a specific model"""
        if model_key not in self.configs:
            return {}
        
        config = self.configs[model_key]
        return {
            'name': config.name,
            'type': config.model_type,
            'model_id': config.model_id,
            'configured': bool(config.api_key)
        }
    
    def is_model_available(self, model_key: str) -> bool:
        """Check if a model is properly configured and available"""
        if model_key not in self.configs:
            return False

        config = self.configs[model_key]

        if config.model_type == "deepseek":
            return bool(config.api_key)

        return False


# Global model manager instance
model_manager = ModelManager()
