from langchain_core.prompts import ChatPromptTemplate
from langgraph.graph import StateGraph, START, END
import time
from datetime import datetime

from src.models.state_models import Code, State
from src.config.model_config import model_manager

from src.prompts.system_prompts import (
    TASK_PLANNER_PROMPT,
    PYTHON_CODE_PROMPT,
    FORMAT_RESULT_PROMPT
)

def print_step(step_name: str, message: str = "", status: str = "INFO"):
    """打印执行步骤信息"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    status_symbols = {
        "INFO": "🔄",
        "SUCCESS": "✅",
        "ERROR": "❌",
        "WARNING": "⚠️",
        "START": "🚀",
        "END": "🎯"
    }
    symbol = status_symbols.get(status, "📝")

    print(f"\n[{timestamp}] {symbol} {step_name}")
    if message:
        print(f"    {message}")
    print("-" * 60)

def fix_plotly_code_errors(code: str, error_msg: str) -> str:
    """修复常见的Plotly代码错误"""
    print(f"🔧 修复Plotly代码错误: {error_msg}")

    # 常见的Plotly方法错误修复
    fixes = {
        'update_xaxis': 'update_xaxes',
        'update_yaxis': 'update_yaxes',
        '.update_xaxis(': '.update_xaxes(',
        '.update_yaxis(': '.update_yaxes(',
        'fig.update_xaxis': 'fig.update_xaxes',
        'fig.update_yaxis': 'fig.update_yaxes',
    }

    fixed_code = code
    for wrong_method, correct_method in fixes.items():
        if wrong_method in fixed_code:
            fixed_code = fixed_code.replace(wrong_method, correct_method)
            print(f"  📝 修复: {wrong_method} → {correct_method}")

    # 如果是Figure对象没有某个属性的错误，尝试添加正确的导入
    if "'Figure' object has no attribute" in error_msg:
        if "import plotly.graph_objects as go" not in fixed_code:
            # 在导入部分添加go导入
            import_lines = []
            code_lines = []
            for line in fixed_code.split('\n'):
                if line.strip().startswith('import ') or line.strip().startswith('from '):
                    import_lines.append(line)
                else:
                    code_lines.append(line)

            if "import plotly.graph_objects as go" not in '\n'.join(import_lines):
                import_lines.append("import plotly.graph_objects as go")
                print("  📦 添加导入: import plotly.graph_objects as go")

            fixed_code = '\n'.join(import_lines + [''] + code_lines)

    return fixed_code

def clean_dataframe_for_arrow(df, name: str = "DataFrame"):
    """清理DataFrame以确保PyArrow兼容性"""
    import pandas as pd

    print(f"🔧 清理DataFrame '{name}': {len(df)} 行 × {len(df.columns)} 列")
    cleaned_df = df.copy()

    for col in cleaned_df.columns:
        original_dtype = cleaned_df[col].dtype

        # 处理object类型列（最容易出现PyArrow问题）
        if cleaned_df[col].dtype == 'object':
            # 填充空值
            if cleaned_df[col].isnull().any():
                cleaned_df[col] = cleaned_df[col].fillna('')

            # 检查列中的数据类型
            sample_values = cleaned_df[col].dropna().head(10)
            if len(sample_values) > 0:
                # 检查是否主要是数值
                numeric_count = 0
                for val in sample_values:
                    try:
                        float(val)
                        numeric_count += 1
                    except (ValueError, TypeError):
                        pass

                # 如果大部分是数值，尝试转换
                if numeric_count / len(sample_values) > 0.7:
                    try:
                        cleaned_df[col] = pd.to_numeric(cleaned_df[col], errors='coerce').fillna(0)
                        print(f"  📊 列 '{col}': {original_dtype} → numeric")
                        continue
                    except:
                        pass

            # 统一转换为字符串
            cleaned_df[col] = cleaned_df[col].astype(str)
            print(f"  📝 列 '{col}': {original_dtype} → string")

        # 处理混合类型的数值列
        elif cleaned_df[col].dtype.name in ['int64', 'float64'] and cleaned_df[col].isnull().any():
            cleaned_df[col] = cleaned_df[col].fillna(0)
            print(f"  🔢 列 '{col}': 填充空值为0")

        # 处理datetime列
        elif 'datetime' in str(cleaned_df[col].dtype):
            try:
                cleaned_df[col] = pd.to_datetime(cleaned_df[col], errors='coerce')
                print(f"  📅 列 '{col}': 标准化datetime格式")
            except:
                cleaned_df[col] = cleaned_df[col].astype(str)
                print(f"  📝 列 '{col}': datetime转换失败，改为string")

    print(f"✅ DataFrame '{name}' 清理完成")
    return cleaned_df

def df_schema_preview(df):
    data_frame_preview = df.head(2).to_markdown()

    available_columns = "| Column Name |\n|--------|\n"
    for col in df.columns:
        available_columns += f"| {col} |\n"

    column_data_types = "| Column Name | Data Type |\n|---------|--------|\n"
    for col, dtype in df.dtypes.items():
        column_data_types += f"| {col} | {dtype} |\n"
    
    return data_frame_preview, available_columns, column_data_types

def create_workflow(df, model_key: str = None):
    """Create workflow with specified model or current model"""

    print("\n" + "="*80)
    print("🚀 智能数据分析工作流启动")
    print("="*80)
    print(f"📊 数据集规模: {len(df)} 行 × {len(df.columns)} 列")
    print(f"🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Get the LLM instance from model manager
    if model_key:
        llm = model_manager.set_current_model(model_key)
        print(f"🤖 使用指定模型: {model_key}")
    else:
        llm = model_manager.get_current_model()
        if llm is None:
            raise ValueError("No model is currently configured. Please set a model first.")
        print(f"🤖 使用当前模型: {model_manager.current_model}")

    print("="*80)

    def plan_task(state: State) -> dict:
        print_step("任务规划阶段", "开始分析用户需求并制定执行计划", "START")

        print(f"📋 用户查询: {state['user_query']}")
        print(f"📊 数据集信息: {len(df)} 行 × {len(df.columns)} 列")

        data_frame_preview, available_columns, column_data_types = df_schema_preview(df)
        print(f"🔍 数据预览已生成，包含 {len(df.columns)} 个字段")

        prompt = ChatPromptTemplate.from_messages([
            ("system", TASK_PLANNER_PROMPT),
            ("human", """Dataframe Schema:
             {data_frame_preview}

             Available Columns:
             {available_columns}

             Column Data Types:
             {column_data_types}

             User Question:
             {user_question}

             Give a proper task plan to solve the user query:""")
        ])

        print("🤖 正在调用AI模型生成任务计划...")
        start_time = time.time()

        task_planner_llm = llm
        task_chain = prompt | task_planner_llm
        response = task_chain.invoke({
            "data_frame_preview": data_frame_preview,
            "available_columns": available_columns,
            "column_data_types": column_data_types,
            "user_question": state["user_query"]
        })

        elapsed_time = time.time() - start_time
        print_step("任务规划完成", f"耗时: {elapsed_time:.2f}秒", "SUCCESS")
        print(f"📝 生成的计划:\n{response.content[:200]}...")

        return {"task_plan": response.content}

    def execute_task(state: State) -> dict:
        error = state["error"]
        iterations = state["iterations"]

        if error:
            print_step("代码修复阶段", f"第 {iterations + 1} 次尝试 - 修复错误", "WARNING")
            print(f"❌ 上次执行错误: {error}")
            print("🔧 正在生成修复后的代码...")

            start_time = time.time()
            code = state["code"]
            task_plan = state["task_plan"]

            # 首先尝试自动修复常见的Plotly错误
            if "Figure" in error and ("update_xaxis" in error or "update_yaxis" in error):
                print("🔧 检测到Plotly方法错误，尝试自动修复...")
                fixed_code = fix_plotly_code_errors(code, error)
                if fixed_code != code:
                    elapsed_time = time.time() - start_time
                    print_step("自动修复完成", f"耗时: {elapsed_time:.2f}秒", "SUCCESS")
                    print(f"🔄 尝试次数: {iterations + 1}")
                    return {"code": fixed_code, "iterations": iterations + 1}

            # 如果自动修复无效，使用AI修复
            retry_llm = llm.with_structured_output(Code)
            enhanced_prompt = f"""
            You have been given error: {error} and Python code: {code} along with Task Plan {task_plan}.

            Common Plotly fixes needed:
            - Use fig.update_xaxes() instead of fig.update_xaxis()
            - Use fig.update_yaxes() instead of fig.update_yaxis()
            - Use fig.update_layout() for general layout updates
            - Make sure to import plotly.graph_objects as go if using go.Figure

            Please fix the error and generate the correct python code to perform the task plan.
            Do not change the task plan and the column names. Use the existing 'df' and strictly follow the given plan.
            """

            response = retry_llm.invoke(enhanced_prompt)

            elapsed_time = time.time() - start_time
            print_step("AI代码修复完成", f"耗时: {elapsed_time:.2f}秒", "SUCCESS")
            print(f"🔄 尝试次数: {iterations + 1}")
            return {"code": response.final_code, "iterations": iterations + 1}
        else:
            print_step("代码生成阶段", "根据任务计划生成Python代码", "START")
            print("💻 正在基于任务计划生成可执行代码...")

            prompt = ChatPromptTemplate.from_messages([
                ("system", PYTHON_CODE_PROMPT),
                ("human", """Dataframe Schema:
                 {data_frame_preview}

                 Available Columns:
                 {available_columns}

                 Column Data Types:
                 {column_data_types}

                 Execution Plan:
                 {execution_plan}

                 User Question:
                 {user_question}

                 Write the correct Python code to perform the task plan:""")
            ])

            data_frame_preview, available_columns, column_data_types = df_schema_preview(df)

            start_time = time.time()
            task_plan = state["task_plan"]
            code_llm = llm.with_structured_output(Code)
            code_chain = prompt | code_llm

            response = code_chain.invoke({
                "data_frame_preview": data_frame_preview,
                "available_columns": available_columns,
                "column_data_types": column_data_types,
                "execution_plan": task_plan,
                "user_question": state["user_query"],
            })

            elapsed_time = time.time() - start_time
            iterations = state["iterations"]
            print_step("代码生成完成", f"耗时: {elapsed_time:.2f}秒", "SUCCESS")
            print(f"🔄 尝试次数: {iterations + 1}")
            print(f"📝 生成的代码长度: {len(response.final_code)} 字符")
            return {"code": response.final_code, "iterations": iterations + 1}

    def execute_with_exec(state: State) -> str:
        iterations = state["iterations"]
        code = state["code"]

        print_step("代码执行阶段", f"第 {iterations} 次尝试执行生成的代码", "START")
        print(f"📝 执行代码预览:\n{code[:200]}...")

        try:
            print("🔧 准备执行环境...")
            import pandas as pd
            import plotly.express as px
            import numpy as np
            import re
            from datetime import datetime as dt
            import plotly.graph_objects as go

            exec_globals = {
                "df": df,
                "pd": pd,
                "px": px,
                "np": np,
                "re": re,
                "dt": dt,
                "go": go
            }
            exec_locals = {}

            print("⚡ 开始执行代码...")
            start_time = time.time()

            exec(code, exec_globals, exec_locals)

            elapsed_time = time.time() - start_time

            if "output_dict" not in exec_locals:
                raise ValueError("Missing output_dict")

            # 清理输出数据，修复PyArrow兼容性问题
            print("🔧 开始清理输出数据以确保PyArrow兼容性...")
            cleaned_output = {}
            for key, value in exec_locals["output_dict"].items():
                if isinstance(value, pd.DataFrame):
                    # 使用专门的清理函数
                    cleaned_output[key] = clean_dataframe_for_arrow(value, key)
                else:
                    cleaned_output[key] = value

            output_keys = list(cleaned_output.keys())
            print_step("代码执行成功", f"耗时: {elapsed_time:.2f}秒", "SUCCESS")
            print(f"🎯 第 {iterations} 次尝试成功!")
            print(f"📊 生成结果: {len(output_keys)} 个输出项")
            print(f"🔑 输出键: {', '.join(output_keys)}")

            return {"output": cleaned_output, "error": None}

        except Exception as e:
            print_step("代码执行失败", f"第 {iterations} 次尝试失败", "ERROR")
            print(f"❌ 错误信息: {str(e)}")
            print(f"🔄 将尝试修复代码...")
            return {"code": code, "error": str(e)}

    def retry_code(state: State) -> str:
        error = state["error"]
        iterations = state["iterations"]

        if error == None:
            print_step("执行流程", "代码执行成功，进入结果格式化阶段", "SUCCESS")
            return "END"
        if iterations < 3:
            print_step("重试决策", f"第 {iterations} 次尝试失败，准备重试", "WARNING")
            print(f"🔄 将进行第 {iterations + 1} 次尝试")
            return "RETRY"
        print_step("执行终止", f"经过 {iterations} 次尝试仍然失败，停止执行", "ERROR")
        print("❌ 已达到最大重试次数，分析失败")
        state["output"] = None
        return "STOP"

    def format_result(state: State) -> dict:
        print_step("结果格式化阶段", "生成用户友好的分析报告", "START")
        print("📝 正在整理分析结果...")

        prompt = ChatPromptTemplate.from_messages([
            ("system", FORMAT_RESULT_PROMPT),
            ("human", """User Question:
             {user_question}

             Python Results:
             {result}

             Formatted response:"""),
        ])

        start_time = time.time()
        format_chain = prompt | llm
        response = format_chain.invoke({
            "user_question": state["user_query"],
            "result": state["output"]
        })

        elapsed_time = time.time() - start_time
        print_step("分析完成", f"耗时: {elapsed_time:.2f}秒", "END")
        print(f"📊 生成分析报告长度: {len(response.content)} 字符")
        print("🎉 智能数据分析流程全部完成!")

        return {"answer": response.content}


    workflow = StateGraph(State)


    workflow.add_node("Plan Task", plan_task)
    workflow.add_node("Execute Task", execute_task)
    workflow.add_node("Code Execution", execute_with_exec)
    workflow.add_node("Format Result", format_result)


    workflow.add_edge(START, "Plan Task")
    workflow.add_edge("Plan Task", "Execute Task")
    workflow.add_edge("Execute Task", "Code Execution")
    workflow.add_conditional_edges(
        "Code Execution",
        retry_code,
        {
            "RETRY": "Execute Task",
            "STOP": END,
            "END": "Format Result"
        }
    )
    workflow.add_edge("Format Result", END)

    return workflow.compile()