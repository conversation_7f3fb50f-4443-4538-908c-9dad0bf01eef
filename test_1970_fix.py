#!/usr/bin/env python3
"""
测试1970年问题修复
"""

import pandas as pd
import numpy as np
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_year_cleaning():
    """测试年份清理功能"""
    print("🧪 测试年份清理功能")
    print("=" * 60)
    
    # 导入清理函数
    from src.graph.workflow import clean_year_column, clean_dataframe_for_arrow
    
    # 创建包含各种年份格式的测试数据
    test_data = {
        '项目名称': ['项目A', '项目B', '项目C', '项目D', '项目E', '项目F'],
        '项目实施年度': [
            '2016-2019',  # 年份范围
            2020,         # 整数年份
            '2021',       # 字符串年份
            'nan',        # 异常值
            None,         # 空值
            '2018-2022'   # 另一个年份范围
        ],
        '其他列': ['A', 'B', 'C', 'D', 'E', 'F']
    }
    
    df = pd.DataFrame(test_data)
    print("📊 原始测试数据:")
    print(df)
    print(f"\n数据类型:")
    print(df.dtypes)
    
    # 测试年份清理
    print(f"\n🔧 应用年份清理...")
    cleaned_df = clean_year_column(df.copy(), '项目实施年度')
    
    print(f"\n📊 清理后数据:")
    print(cleaned_df)
    print(f"\n清理后数据类型:")
    print(cleaned_df.dtypes)
    
    # 检查是否有1970年
    year_values = cleaned_df['项目实施年度'].dropna()
    if len(year_values) > 0:
        min_year = year_values.min()
        max_year = year_values.max()
        print(f"\n📅 年份范围: {min_year} - {max_year}")
        
        if min_year < 1990:
            print(f"⚠️ 发现异常年份: {min_year}")
        else:
            print(f"✅ 年份范围正常")
    
    return cleaned_df

def test_real_data():
    """测试真实数据"""
    print(f"\n🧪 测试真实数据")
    print("=" * 60)
    
    try:
        # 读取真实数据
        df = pd.read_excel('data/赤水市农村项目资产台账.xlsx')
        df_clean = df.dropna(how='all')
        
        print(f"📊 原始数据: {len(df_clean)} 行 × {len(df_clean.columns)} 列")
        
        # 检查项目实施年度列
        year_col = '项目实施年度'
        print(f"\n🔍 原始年份数据分析:")
        print(f"  数据类型: {df_clean[year_col].dtype}")
        print(f"  唯一值数量: {df_clean[year_col].nunique()}")
        
        # 显示前10个唯一值
        unique_years = df_clean[year_col].value_counts().head(10)
        print(f"  前10个年份值:")
        for year, count in unique_years.items():
            print(f"    {repr(year)}: {count}次")
        
        # 应用清理
        from src.graph.workflow import clean_dataframe_for_arrow
        print(f"\n🔧 应用完整数据清理...")
        cleaned_df = clean_dataframe_for_arrow(df_clean, "赤水市农村项目资产台账")
        
        # 检查清理后的年份
        print(f"\n📊 清理后年份分析:")
        cleaned_years = cleaned_df[year_col].dropna()
        if len(cleaned_years) > 0:
            print(f"  年份范围: {cleaned_years.min()} - {cleaned_years.max()}")
            print(f"  唯一年份数量: {cleaned_years.nunique()}")
            
            # 检查是否有异常年份
            abnormal_years = cleaned_years[(cleaned_years < 1990) | (cleaned_years > 2030)]
            if len(abnormal_years) > 0:
                print(f"⚠️ 发现 {len(abnormal_years)} 个异常年份")
                print(f"  异常年份: {abnormal_years.unique()}")
            else:
                print(f"✅ 所有年份都在正常范围内")
        
        # 测试PyArrow兼容性
        print(f"\n🔧 测试PyArrow兼容性...")
        try:
            import pyarrow as pa
            table = pa.Table.from_pandas(cleaned_df)
            print(f"✅ PyArrow转换成功")
        except Exception as e:
            print(f"❌ PyArrow转换失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 真实数据测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_analysis_with_years():
    """模拟可能导致1970年问题的分析场景"""
    print(f"\n🧪 模拟分析场景")
    print("=" * 60)
    
    try:
        # 读取数据
        df = pd.read_excel('data/赤水市农村项目资产台账.xlsx')
        df_clean = df.dropna(how='all')
        
        # 应用清理
        from src.graph.workflow import clean_dataframe_for_arrow
        cleaned_df = clean_dataframe_for_arrow(df_clean, "测试数据")
        
        # 模拟常见的年份分析操作
        year_col = '项目实施年度'
        
        print(f"📊 执行年份分析...")
        
        # 1. 年份分布统计
        year_dist = cleaned_df[year_col].value_counts().sort_index()
        print(f"年份分布:")
        for year, count in year_dist.head(10).items():
            print(f"  {year}: {count}个项目")
        
        # 2. 年份范围检查
        years = cleaned_df[year_col].dropna()
        if len(years) > 0:
            print(f"\n年份统计:")
            print(f"  最早年份: {years.min()}")
            print(f"  最晚年份: {years.max()}")
            print(f"  年份跨度: {years.max() - years.min()}年")
            
            # 检查是否有1970年
            if 1970 in years.values:
                print(f"❌ 发现1970年异常值!")
                return False
            else:
                print(f"✅ 未发现1970年异常值")
                return True
        
    except Exception as e:
        print(f"❌ 分析模拟失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 1970年问题修复测试")
    print("=" * 80)
    
    # 1. 测试年份清理功能
    test_year_cleaning()
    
    # 2. 测试真实数据
    real_data_ok = test_real_data()
    
    # 3. 模拟分析场景
    analysis_ok = simulate_analysis_with_years()
    
    # 总结
    print(f"\n" + "=" * 80)
    print(f"📋 测试结果总结:")
    print(f"年份清理功能: ✅ 正常")
    print(f"真实数据处理: {'✅ 成功' if real_data_ok else '❌ 失败'}")
    print(f"分析场景模拟: {'✅ 成功' if analysis_ok else '❌ 失败'}")
    
    if real_data_ok and analysis_ok:
        print(f"\n🎉 1970年问题修复成功!")
        print(f"💡 现在可以安全地进行年份相关分析，不会出现1970年异常值")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步调试")
    
    return real_data_ok and analysis_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
