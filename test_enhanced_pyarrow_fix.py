#!/usr/bin/env python3
"""
测试增强的PyArrow兼容性修复功能
"""

import pandas as pd
import numpy as np
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def create_problematic_dataframe():
    """创建包含各种PyArrow问题的测试DataFrame"""
    print("🔧 创建包含PyArrow兼容性问题的测试数据...")
    
    # 创建各种问题数据
    data = {
        '混合类型列1': [2020, '2021', 2022, '2023', None],  # 数字和字符串混合
        '混合类型列2': [100, 'N/A', 200, '300', np.nan],   # 数字和字符串混合
        '纯数字字符串': ['100', '200', '300', '400', ''],   # 看起来像数字的字符串
        '纯字符串': ['北京', '上海', '广州', '深圳', None],    # 纯字符串
        '全空列': [None, np.nan, '', None, np.nan],        # 全空列
        '数值列': [1.5, 2.0, np.nan, 4.5, 5.0],           # 包含NaN的数值列
        '特殊值': ['nan', 'None', 'NaN', '<NA>', '正常值'],  # 包含特殊字符串值
    }
    
    df = pd.DataFrame(data)
    print(f"📊 创建测试数据: {len(df)} 行 × {len(df.columns)} 列")
    
    # 显示数据类型信息
    print("\n📋 原始数据类型:")
    for col in df.columns:
        print(f"  {col}: {df[col].dtype}")
        sample_values = df[col].dropna().head(3).tolist()
        print(f"    样本值: {sample_values}")
    
    return df

def test_pyarrow_conversion(df, name="测试数据"):
    """测试PyArrow转换"""
    print(f"\n🧪 测试 {name} 的PyArrow转换...")
    
    try:
        import pyarrow as pa
        table = pa.Table.from_pandas(df)
        print(f"✅ {name} PyArrow转换成功")
        return True
    except Exception as e:
        print(f"❌ {name} PyArrow转换失败: {e}")
        return False

def test_streamlit_compatibility(df, name="测试数据"):
    """测试Streamlit兼容性（模拟）"""
    print(f"\n📱 测试 {name} 的Streamlit兼容性...")
    
    try:
        # 模拟Streamlit的DataFrame序列化过程
        import pyarrow as pa
        from pyarrow import pandas_compat
        
        # 这是Streamlit内部使用的转换过程
        table = pa.Table.from_pandas(df)
        serialized = table.to_pandas()
        print(f"✅ {name} Streamlit兼容性测试通过")
        return True
    except Exception as e:
        print(f"❌ {name} Streamlit兼容性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 增强PyArrow兼容性修复功能测试")
    print("=" * 80)
    
    # 创建问题数据
    problematic_df = create_problematic_dataframe()
    
    # 测试原始数据的PyArrow兼容性
    original_pyarrow_ok = test_pyarrow_conversion(problematic_df, "原始数据")
    original_streamlit_ok = test_streamlit_compatibility(problematic_df, "原始数据")
    
    print("\n" + "=" * 80)
    print("🔧 应用增强的PyArrow兼容性修复...")
    
    try:
        from src.graph.workflow import clean_dataframe_for_arrow
        
        # 应用修复
        cleaned_df = clean_dataframe_for_arrow(problematic_df, "问题数据")
        
        print(f"\n📊 修复后数据: {len(cleaned_df)} 行 × {len(cleaned_df.columns)} 列")
        
        # 显示修复后的数据类型
        print("\n📋 修复后数据类型:")
        for col in cleaned_df.columns:
            print(f"  {col}: {cleaned_df[col].dtype}")
            sample_values = cleaned_df[col].head(3).tolist()
            print(f"    样本值: {sample_values}")
        
        # 测试修复后的PyArrow兼容性
        print("\n" + "=" * 80)
        cleaned_pyarrow_ok = test_pyarrow_conversion(cleaned_df, "修复后数据")
        cleaned_streamlit_ok = test_streamlit_compatibility(cleaned_df, "修复后数据")
        
        # 测试结果总结
        print("\n" + "=" * 80)
        print("📋 测试结果总结:")
        print(f"原始数据 PyArrow兼容性: {'✅ 通过' if original_pyarrow_ok else '❌ 失败'}")
        print(f"原始数据 Streamlit兼容性: {'✅ 通过' if original_streamlit_ok else '❌ 失败'}")
        print(f"修复后数据 PyArrow兼容性: {'✅ 通过' if cleaned_pyarrow_ok else '❌ 失败'}")
        print(f"修复后数据 Streamlit兼容性: {'✅ 通过' if cleaned_streamlit_ok else '❌ 失败'}")
        
        # 计算修复效果
        if not original_pyarrow_ok and cleaned_pyarrow_ok:
            print("\n🎉 PyArrow兼容性修复成功!")
        if not original_streamlit_ok and cleaned_streamlit_ok:
            print("🎉 Streamlit兼容性修复成功!")
        
        success = cleaned_pyarrow_ok and cleaned_streamlit_ok
        
        if success:
            print("\n✅ 增强PyArrow兼容性修复功能测试全部通过!")
            print("💡 系统现在可以处理更复杂的混合数据类型问题")
            print("🔧 支持的修复类型:")
            print("  - 混合数字/字符串列的智能转换")
            print("  - 特殊值的清理和标准化")
            print("  - 全空列的安全处理")
            print("  - 数值列NaN值的填充")
            print("  - 详细的转换过程监控")
        else:
            print("\n❌ 部分测试失败，需要进一步优化")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_data():
    """测试真实数据文件"""
    print("\n" + "=" * 80)
    print("📊 测试真实数据文件...")
    
    try:
        # 尝试加载真实数据文件
        data_file = "data/赤水市农村项目资产台账.xlsx"
        
        try:
            df = pd.read_excel(data_file)
            print(f"✅ 成功加载真实数据: {len(df)} 行 × {len(df.columns)} 列")
            
            # 测试原始数据
            original_ok = test_pyarrow_conversion(df, "真实数据(原始)")
            
            if not original_ok:
                print("🔧 应用修复...")
                from src.graph.workflow import clean_dataframe_for_arrow
                cleaned_df = clean_dataframe_for_arrow(df, "真实数据")
                
                # 测试修复后数据
                cleaned_ok = test_pyarrow_conversion(cleaned_df, "真实数据(修复后)")
                
                if cleaned_ok:
                    print("🎉 真实数据PyArrow兼容性修复成功!")
                    return True
                else:
                    print("❌ 真实数据修复失败")
                    return False
            else:
                print("✅ 真实数据原本就兼容PyArrow")
                return True
                
        except FileNotFoundError:
            print("⚠️ 真实数据文件未找到，跳过真实数据测试")
            return True
        except Exception as e:
            print(f"❌ 加载真实数据失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 真实数据测试失败: {e}")
        return False

if __name__ == "__main__":
    # 测试合成数据
    synthetic_success = main()
    
    # 测试真实数据
    real_data_success = test_real_data()
    
    print("\n" + "=" * 80)
    print("🎯 最终测试结果:")
    print(f"合成数据测试: {'✅ 成功' if synthetic_success else '❌ 失败'}")
    print(f"真实数据测试: {'✅ 成功' if real_data_success else '❌ 失败'}")
    
    overall_success = synthetic_success and real_data_success
    
    if overall_success:
        print("\n🎉 增强PyArrow兼容性修复功能全面测试通过!")
        print("🚀 系统现在具备更强大的数据兼容性处理能力")
    else:
        print("\n❌ 部分测试失败，需要进一步优化")
    
    exit(0 if overall_success else 1)
