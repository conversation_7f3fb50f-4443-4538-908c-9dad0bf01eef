#!/usr/bin/env python3
"""
测试增强的工作流打印功能
"""

import pandas as pd
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_enhanced_workflow():
    """测试增强的工作流"""
    print("🧪 测试增强的工作流打印功能")
    print("=" * 60)
    
    try:
        from src.graph.workflow import create_workflow
        from src.config.model_config import model_manager
        
        # 创建测试数据
        df = pd.DataFrame({
            '产品名称': ['苹果手机', '华为平板', '小米耳机', '联想电脑', '三星电视'],
            '销售额': [15000, 8500, 2300, 12000, 9800],
            '地区': ['北京', '上海', '广州', '深圳', '成都'],
            '销售日期': ['2024-01-15', '2024-01-16', '2024-01-17', '2024-01-18', '2024-01-19']
        })
        
        print(f"📊 测试数据创建完成: {len(df)} 行 × {len(df.columns)} 列")
        
        # 检查模型配置
        available_models = model_manager.get_available_models()
        if not available_models:
            print("❌ 没有可用的模型配置")
            return False

        print(f"🤖 可用模型: {list(available_models.keys())}")

        # 设置当前模型
        model_key = list(available_models.keys())[0]
        model_manager.set_current_model(model_key)
        print(f"🔧 设置当前模型: {model_key}")

        # 创建工作流
        workflow = create_workflow(df)
        print("✅ 工作流创建成功")
        
        # 测试查询
        test_query = "分析各地区的销售情况，找出销售额最高的地区"
        print(f"\n🔍 测试查询: {test_query}")
        
        # 执行工作流
        result = workflow.invoke({
            "user_query": test_query,
            "error": None,
            "iterations": 0
        })
        
        print("\n" + "="*80)
        print("🎉 工作流执行完成!")
        print("="*80)
        
        # 显示结果摘要
        if result.get("task_plan"):
            print(f"📋 任务计划: {len(result['task_plan'])} 字符")
        
        if result.get("code"):
            print(f"💻 生成代码: {len(result['code'])} 字符")
        
        if result.get("output"):
            print(f"📊 分析结果: {len(result['output'])} 个输出项")
        
        if result.get("answer"):
            print(f"📝 分析报告: {len(result['answer'])} 字符")
        
        print(f"🔄 总迭代次数: {result.get('iterations', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_enhanced_workflow()
    if success:
        print("\n✅ 增强工作流测试成功!")
        print("💡 现在运行Streamlit应用时，终端将显示详细的执行过程")
    else:
        print("\n❌ 增强工作流测试失败!")
    
    exit(0 if success else 1)
