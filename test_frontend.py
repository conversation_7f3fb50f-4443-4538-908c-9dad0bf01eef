#!/usr/bin/env python3
"""
测试前端问题的简化版本
"""

import streamlit as st
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

st.set_page_config(
    page_title="🔧 前端测试",
    page_icon="🔧",
    layout="wide"
)

st.title("🔧 前端问题诊断")

# 1. 测试环境变量
st.header("1. 环境变量测试")
deepseek_key = os.getenv('DEEPSEEK_API_KEY')
st.write(f"DEEPSEEK_API_KEY: {'✅ 已设置' if deepseek_key else '❌ 未设置'}")
if deepseek_key:
    st.write(f"密钥前缀: {deepseek_key[:15]}...")

# 2. 测试模块导入
st.header("2. 模块导入测试")
try:
    from src.config.model_config import model_manager
    st.success("✅ model_manager 导入成功")
    
    # 测试配置
    configs = model_manager.configs
    st.write(f"配置数量: {len(configs)}")
    
    for key, config in configs.items():
        st.write(f"- {key}: {config.name}")
        st.write(f"  API密钥: {'✅ 有' if config.api_key else '❌ 无'}")
    
    # 测试可用模型
    available = model_manager.get_available_models()
    st.write(f"可用模型: {len(available)}")
    for key, name in available.items():
        st.write(f"- {key}: {name}")
    
    if available:
        st.success("✅ 有可用模型")
        
        # 测试模型创建
        try:
            model_key = list(available.keys())[0]
            llm = model_manager.set_current_model(model_key)
            st.success(f"✅ 模型实例创建成功: {type(llm)}")
        except Exception as e:
            st.error(f"❌ 模型实例创建失败: {e}")
    else:
        st.error("❌ 没有可用模型")
        
except Exception as e:
    st.error(f"❌ model_manager 导入失败: {e}")
    import traceback
    st.code(traceback.format_exc())

# 3. 测试工作流
st.header("3. 工作流测试")
try:
    from src.graph.workflow import create_workflow
    import pandas as pd
    
    st.success("✅ workflow 导入成功")
    
    # 创建测试数据
    df = pd.DataFrame({
        '销售额': [1000, 1500, 1200],
        '地区': ['北京', '上海', '广州']
    })
    
    # 测试工作流创建
    workflow = create_workflow(df)
    st.success("✅ 工作流创建成功")
    
except Exception as e:
    st.error(f"❌ 工作流测试失败: {e}")
    import traceback
    st.code(traceback.format_exc())

# 4. 显示系统信息
st.header("4. 系统信息")
import sys
st.write(f"Python版本: {sys.version}")
st.write(f"工作目录: {os.getcwd()}")
st.write(f"环境变量数量: {len(os.environ)}")

# 5. 测试按钮
if st.button("🔄 重新测试"):
    st.rerun()
