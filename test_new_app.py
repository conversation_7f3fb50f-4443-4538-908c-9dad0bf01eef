#!/usr/bin/env python3
"""
测试新的应用程序结构
"""

import pandas as pd
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_model_manager():
    """测试模型管理器"""
    print("🔧 测试模型管理器...")
    
    try:
        from src.config.model_config import model_manager
        
        # 检查可用模型
        available_models = model_manager.get_available_models()
        print(f"✅ 可用模型: {available_models}")
        
        if available_models:
            # 选择第一个模型
            model_key = list(available_models.keys())[0]
            model_name = available_models[model_key]
            
            # 创建模型实例
            llm = model_manager.set_current_model(model_key)
            print(f"✅ 模型已配置: {model_name}")
            
            return llm
        else:
            print("❌ 没有可用模型")
            return None
            
    except Exception as e:
        print(f"❌ 模型管理器测试失败: {e}")
        return None

def test_workflow():
    """测试工作流"""
    print("\n🔄 测试工作流...")
    
    try:
        from src.graph.workflow import create_workflow
        
        # 创建测试数据
        df = pd.DataFrame({
            '销售额': [1000, 1500, 1200, 1800, 2000],
            '地区': ['北京', '上海', '广州', '深圳', '北京'],
            '产品': ['A', 'B', 'A', 'C', 'B']
        })
        
        # 获取模型
        llm = test_model_manager()
        if not llm:
            return False
        
        # 创建工作流
        workflow = create_workflow(df)
        print("✅ 工作流创建成功")
        
        # 测试简单查询
        print("\n📊 测试数据分析...")
        result = workflow.invoke({
            "user_query": "分析各地区的销售情况",
            "error": None,
            "iterations": 0
        })
        
        print("✅ 分析完成")
        print(f"计划: {result.get('task_plan', '无')[:100]}...")
        print(f"代码: {result.get('code', '无')[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 工作流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_app_structure():
    """测试应用程序结构"""
    print("\n🏗️ 测试应用程序结构...")
    
    # 检查关键模块
    modules_to_test = [
        'src.config.model_config',
        'src.graph.workflow',
        'src.prompts.optimized_prompts'
    ]
    
    success_count = 0
    for module in modules_to_test:
        try:
            __import__(module)
            print(f"✅ {module}")
            success_count += 1
        except Exception as e:
            print(f"❌ {module}: {e}")
    
    print(f"\n📊 模块测试结果: {success_count}/{len(modules_to_test)} 成功")
    return success_count == len(modules_to_test)

def main():
    """主测试函数"""
    print("🚀 测试新的应用程序结构")
    print("=" * 50)
    
    # 测试应用程序结构
    structure_ok = test_app_structure()
    
    # 测试工作流
    if structure_ok:
        workflow_ok = test_workflow()
    else:
        workflow_ok = False
    
    # 总结
    print(f"\n📋 测试结果总结:")
    print(f"应用程序结构: {'✅ 正常' if structure_ok else '❌ 异常'}")
    print(f"工作流功能: {'✅ 正常' if workflow_ok else '❌ 异常'}")
    
    if structure_ok and workflow_ok:
        print(f"\n🎉 新应用程序结构测试通过!")
        print(f"\n📖 主要改进:")
        print("1. ✅ 删除了前端模型配置界面")
        print("2. ✅ 使用后端model_manager自动管理模型")
        print("3. ✅ 简化了用户操作流程")
        print("4. ✅ 优化了界面设计和用户体验")
        print("5. ✅ 集成了优化的提示词系统")
        
        print(f"\n🚀 启动应用程序:")
        print("streamlit run app.py --server.port 8505")
        print("然后访问: http://localhost:8505")
        
    else:
        print(f"\n❌ 测试失败，需要修复问题")
    
    return structure_ok and workflow_ok

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
