#!/usr/bin/env python3
"""
测试Plotly错误修复功能
"""

import pandas as pd
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_plotly_error_fix():
    """测试Plotly错误修复功能"""
    print("🧪 测试Plotly错误修复功能")
    print("=" * 60)
    
    try:
        from src.graph.workflow import fix_plotly_code_errors
        
        # 测试用例1: update_xaxis错误
        print("\n1. 测试 update_xaxis 错误修复:")
        wrong_code1 = """
import pandas as pd
import plotly.express as px

fig = px.bar(df, x='category', y='value')
fig.update_xaxis(title='Category')
fig.update_yaxis(title='Value')
"""
        
        error_msg1 = "'Figure' object has no attribute 'update_xaxis'"
        fixed_code1 = fix_plotly_code_errors(wrong_code1, error_msg1)
        
        print("原始代码:")
        print(wrong_code1)
        print("修复后代码:")
        print(fixed_code1)
        
        # 验证修复
        if "update_xaxes" in fixed_code1 and "update_yaxes" in fixed_code1:
            print("✅ 修复成功")
        else:
            print("❌ 修复失败")
        
        # 测试用例2: 缺少go导入
        print("\n2. 测试缺少go导入的修复:")
        wrong_code2 = """
import pandas as pd
import plotly.express as px

fig = px.bar(df, x='category', y='value')
fig.update_layout(title='Test Chart')
"""
        
        error_msg2 = "'Figure' object has no attribute 'some_method'"
        fixed_code2 = fix_plotly_code_errors(wrong_code2, error_msg2)
        
        print("原始代码:")
        print(wrong_code2)
        print("修复后代码:")
        print(fixed_code2)
        
        # 验证修复
        if "import plotly.graph_objects as go" in fixed_code2:
            print("✅ 导入添加成功")
        else:
            print("❌ 导入添加失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_real_plotly_error():
    """使用真实数据测试Plotly错误修复"""
    print("\n" + "=" * 60)
    print("🔄 使用真实数据测试Plotly错误修复")
    print("=" * 60)
    
    try:
        from src.graph.workflow import create_workflow
        from src.config.model_config import model_manager
        
        # 创建包含可能导致Plotly错误的测试数据
        df = pd.DataFrame({
            '年份': ['2020', '2021', '2022', '2023'],
            '销售额': [1000, 1500, 1200, 1800],
            '地区': ['北京', '上海', '广州', '深圳']
        })
        
        # 设置模型
        available_models = model_manager.get_available_models()
        if not available_models:
            print("❌ 没有可用的模型配置")
            return False
        
        model_key = list(available_models.keys())[0]
        model_manager.set_current_model(model_key)
        
        # 创建工作流
        workflow = create_workflow(df)
        
        # 执行可能导致Plotly错误的查询
        test_query = "创建一个柱状图显示各年份的销售额，并美化图表的坐标轴标题"
        
        print(f"📋 测试查询: {test_query}")
        
        result = workflow.invoke({
            "user_query": test_query,
            "error": None,
            "iterations": 0
        })
        
        if result.get("output"):
            print("✅ 查询执行成功")
            print(f"📊 生成结果: {len(result['output'])} 个输出项")
            
            # 检查是否有图表输出
            has_chart = False
            for key, value in result["output"].items():
                if hasattr(value, 'to_html'):  # Plotly图表对象
                    has_chart = True
                    print(f"📈 生成图表: {key}")
            
            if has_chart:
                print("✅ Plotly图表生成成功")
            else:
                print("⚠️ 没有生成Plotly图表")
            
            return True
        else:
            print("❌ 查询没有生成输出")
            return False
            
    except Exception as e:
        print(f"❌ 真实数据测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 Plotly错误修复功能测试")
    print("=" * 80)
    
    # 测试错误修复函数
    fix_function_success = test_plotly_error_fix()
    
    # 测试真实场景
    real_data_success = test_with_real_plotly_error()
    
    print("\n" + "=" * 80)
    print("📋 测试结果总结:")
    print(f"错误修复函数: {'✅ 成功' if fix_function_success else '❌ 失败'}")
    print(f"真实数据测试: {'✅ 成功' if real_data_success else '❌ 失败'}")
    
    if fix_function_success and real_data_success:
        print("\n🎉 Plotly错误修复功能测试全部通过!")
        print("💡 系统现在可以自动修复常见的Plotly代码错误")
        print("🔧 支持的修复类型:")
        print("  - update_xaxis → update_xaxes")
        print("  - update_yaxis → update_yaxes") 
        print("  - 自动添加缺失的导入")
        print("  - AI增强的错误修复")
    else:
        print("\n❌ 部分测试失败，需要进一步优化")
    
    return fix_function_success and real_data_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
