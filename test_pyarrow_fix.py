#!/usr/bin/env python3
"""
测试PyArrow兼容性修复
"""

import pandas as pd
import numpy as np
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def create_problematic_dataframe():
    """创建包含混合数据类型的问题DataFrame"""
    data = {
        '项目实施年度': [2023, '2024', 2025, '未定', 2026],  # 混合int和str
        '项目名称': ['项目A', '项目B', '项目C', '项目D', '项目E'],
        '预算金额': [100000, '150000', 200000, None, '250000'],  # 混合数值和字符串
        '状态': ['进行中', '已完成', '计划中', '暂停', '进行中'],
        '开始日期': ['2023-01-01', '2023-06-01', None, '2024-01-01', '2024-03-01'],
        '完成率': [0.8, '90%', 0.6, None, '75%']  # 混合float和字符串
    }
    
    df = pd.DataFrame(data)
    print("🔧 创建问题DataFrame:")
    print(f"  形状: {df.shape}")
    print("  数据类型:")
    for col, dtype in df.dtypes.items():
        print(f"    {col}: {dtype}")
    
    return df

def test_arrow_conversion(df, name="测试DataFrame"):
    """测试DataFrame的PyArrow转换"""
    try:
        import pyarrow as pa
        table = pa.Table.from_pandas(df)
        print(f"✅ {name} PyArrow转换成功")
        return True
    except Exception as e:
        print(f"❌ {name} PyArrow转换失败: {e}")
        return False

def test_pyarrow_compatibility():
    """测试PyArrow兼容性修复"""
    print("🧪 测试PyArrow兼容性修复")
    print("=" * 60)
    
    # 创建问题DataFrame
    problematic_df = create_problematic_dataframe()
    
    # 测试原始DataFrame的PyArrow转换
    print("\n1. 测试原始DataFrame:")
    original_success = test_arrow_conversion(problematic_df, "原始DataFrame")
    
    # 导入清理函数
    try:
        from src.graph.workflow import clean_dataframe_for_arrow
        print("\n2. 应用数据清理:")
        cleaned_df = clean_dataframe_for_arrow(problematic_df, "测试DataFrame")
        
        print("\n3. 清理后的数据类型:")
        for col, dtype in cleaned_df.dtypes.items():
            print(f"    {col}: {dtype}")
        
        print("\n4. 测试清理后的DataFrame:")
        cleaned_success = test_arrow_conversion(cleaned_df, "清理后DataFrame")
        
        print("\n5. 数据内容对比:")
        print("原始数据前3行:")
        print(problematic_df.head(3))
        print("\n清理后数据前3行:")
        print(cleaned_df.head(3))
        
        return cleaned_success
        
    except Exception as e:
        print(f"❌ 清理函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_workflow():
    """测试完整工作流的PyArrow兼容性"""
    print("\n" + "=" * 60)
    print("🔄 测试完整工作流的PyArrow兼容性")
    print("=" * 60)
    
    try:
        from src.graph.workflow import create_workflow
        from src.config.model_config import model_manager
        
        # 创建测试数据
        df = create_problematic_dataframe()
        
        # 设置模型
        available_models = model_manager.get_available_models()
        if not available_models:
            print("❌ 没有可用的模型配置")
            return False
        
        model_key = list(available_models.keys())[0]
        model_manager.set_current_model(model_key)
        
        # 创建工作流
        workflow = create_workflow(df)
        
        # 执行简单查询
        result = workflow.invoke({
            "user_query": "分析项目实施年度的分布情况",
            "error": None,
            "iterations": 0
        })
        
        # 检查结果中的DataFrame是否PyArrow兼容
        if result.get("output"):
            print("\n📊 检查输出结果的PyArrow兼容性:")
            all_compatible = True
            for key, value in result["output"].items():
                if isinstance(value, pd.DataFrame):
                    compatible = test_arrow_conversion(value, f"输出DataFrame '{key}'")
                    all_compatible = all_compatible and compatible
            
            return all_compatible
        else:
            print("❌ 工作流没有生成输出结果")
            return False
            
    except Exception as e:
        print(f"❌ 工作流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 PyArrow兼容性修复测试")
    print("=" * 80)
    
    # 测试数据清理函数
    cleanup_success = test_pyarrow_compatibility()
    
    # 测试完整工作流
    workflow_success = test_with_workflow()
    
    print("\n" + "=" * 80)
    print("📋 测试结果总结:")
    print(f"数据清理功能: {'✅ 成功' if cleanup_success else '❌ 失败'}")
    print(f"工作流兼容性: {'✅ 成功' if workflow_success else '❌ 失败'}")
    
    if cleanup_success and workflow_success:
        print("\n🎉 PyArrow兼容性修复测试全部通过!")
        print("💡 现在Streamlit应用可以正确显示包含混合数据类型的DataFrame")
    else:
        print("\n❌ 部分测试失败，需要进一步修复")
    
    exit(0 if (cleanup_success and workflow_success) else 1)
