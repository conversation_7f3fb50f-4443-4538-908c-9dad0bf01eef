#!/usr/bin/env python3
"""
使用真实数据测试智能体系统
测试文件：赤水市农村项目资产台账.xlsx
"""

import pandas as pd
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def load_and_analyze_data():
    """加载并分析真实数据文件"""
    data_file = r"D:\anas_datas\data\赤水市农村项目资产台账.xlsx"
    
    print("🚀 真实数据测试 - 赤水市农村项目资产台账")
    print("=" * 80)
    
    # 检查文件是否存在
    if not os.path.exists(data_file):
        print(f"❌ 文件不存在: {data_file}")
        return False
    
    print(f"📁 数据文件: {data_file}")
    print(f"📊 文件大小: {os.path.getsize(data_file) / 1024 / 1024:.2f} MB")
    
    try:
        # 读取Excel文件
        print("\n🔍 正在读取Excel文件...")
        df = pd.read_excel(data_file)
        
        print(f"✅ 文件读取成功!")
        print(f"📊 数据规模: {len(df)} 行 × {len(df.columns)} 列")
        
        # 显示基本信息
        print(f"\n📋 数据列信息:")
        for i, col in enumerate(df.columns, 1):
            print(f"  {i:2d}. {col} ({df[col].dtype})")
        
        # 显示数据预览
        print(f"\n👀 数据预览 (前5行):")
        print(df.head())
        
        # 检查数据质量
        print(f"\n🔍 数据质量检查:")
        print(f"  空值统计:")
        null_counts = df.isnull().sum()
        for col, null_count in null_counts.items():
            if null_count > 0:
                print(f"    {col}: {null_count} 个空值 ({null_count/len(df)*100:.1f}%)")
        
        # 检查数据类型问题
        print(f"\n🔧 数据类型分析:")
        object_cols = df.select_dtypes(include=['object']).columns
        print(f"  Object类型列数: {len(object_cols)}")
        for col in object_cols:
            sample_values = df[col].dropna().head(3).tolist()
            print(f"    {col}: {sample_values}")
        
        return df
        
    except Exception as e:
        print(f"❌ 文件读取失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_pyarrow_compatibility(df):
    """测试PyArrow兼容性"""
    print(f"\n🔧 测试PyArrow兼容性...")
    
    try:
        import pyarrow as pa
        table = pa.Table.from_pandas(df)
        print("✅ 原始数据PyArrow转换成功")
        return True
    except Exception as e:
        print(f"❌ 原始数据PyArrow转换失败: {e}")
        
        # 尝试使用清理函数
        try:
            from src.graph.workflow import clean_dataframe_for_arrow
            print("🔧 应用数据清理...")
            cleaned_df = clean_dataframe_for_arrow(df, "赤水市农村项目资产台账")
            
            # 再次测试
            table = pa.Table.from_pandas(cleaned_df)
            print("✅ 清理后数据PyArrow转换成功")
            return True
        except Exception as e2:
            print(f"❌ 清理后仍然失败: {e2}")
            return False

def test_workflow_with_real_data(df):
    """使用真实数据测试完整工作流"""
    print(f"\n🤖 测试智能体工作流...")
    
    try:
        from src.graph.workflow import create_workflow
        from src.config.model_config import model_manager
        
        # 设置模型
        available_models = model_manager.get_available_models()
        if not available_models:
            print("❌ 没有可用的模型配置")
            return False
        
        model_key = list(available_models.keys())[0]
        model_manager.set_current_model(model_key)
        print(f"🤖 使用模型: {model_key}")
        
        # 创建工作流
        workflow = create_workflow(df)
        
        # 定义测试查询
        test_queries = [
            "分析项目资产的分布情况，按照不同类别统计数量和金额",
            "找出资产价值最高的前10个项目",
            "分析项目实施年度的分布，看看哪些年份项目最多"
        ]
        
        results = []
        for i, query in enumerate(test_queries, 1):
            print(f"\n📋 测试查询 {i}: {query}")
            print("-" * 60)
            
            try:
                result = workflow.invoke({
                    "user_query": query,
                    "error": None,
                    "iterations": 0
                })
                
                if result.get("output"):
                    print(f"✅ 查询 {i} 执行成功")
                    print(f"  📊 生成结果: {len(result['output'])} 个输出项")
                    
                    # 检查输出的PyArrow兼容性
                    for key, value in result["output"].items():
                        if isinstance(value, pd.DataFrame):
                            try:
                                import pyarrow as pa
                                pa.Table.from_pandas(value)
                                print(f"  ✅ 输出 '{key}' PyArrow兼容")
                            except Exception as e:
                                print(f"  ❌ 输出 '{key}' PyArrow不兼容: {e}")
                    
                    results.append(True)
                else:
                    print(f"❌ 查询 {i} 没有生成输出")
                    results.append(False)
                    
            except Exception as e:
                print(f"❌ 查询 {i} 执行失败: {e}")
                results.append(False)
        
        success_rate = sum(results) / len(results)
        print(f"\n📊 测试结果: {sum(results)}/{len(results)} 成功 ({success_rate*100:.1f}%)")
        
        return success_rate > 0.5
        
    except Exception as e:
        print(f"❌ 工作流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 真实数据测试 - 赤水市农村项目资产台账")
    print("=" * 80)
    
    # 1. 加载数据
    df = load_and_analyze_data()
    if df is None:
        print("❌ 数据加载失败，测试终止")
        return False
    
    # 2. 测试PyArrow兼容性
    arrow_compatible = test_pyarrow_compatibility(df)
    
    # 3. 测试完整工作流
    workflow_success = test_workflow_with_real_data(df)
    
    # 4. 总结
    print("\n" + "=" * 80)
    print("📋 真实数据测试结果总结:")
    print(f"数据加载: ✅ 成功")
    print(f"PyArrow兼容性: {'✅ 成功' if arrow_compatible else '❌ 失败'}")
    print(f"工作流测试: {'✅ 成功' if workflow_success else '❌ 失败'}")
    
    if arrow_compatible and workflow_success:
        print(f"\n🎉 真实数据测试全部通过!")
        print(f"💡 系统可以正确处理赤水市农村项目资产台账数据")
        print(f"🚀 可以在Streamlit应用中上传此文件进行分析")
    else:
        print(f"\n⚠️ 部分测试失败，但系统基本可用")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
